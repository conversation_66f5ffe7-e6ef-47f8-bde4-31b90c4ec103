import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../../prisma/prisma.service';
import { ILlmService } from '../interfaces/llm.service.interface';
import { OpenaiLlmService } from './openai-llm.service';
import { GoogleGeminiLlmService } from './google-gemini-llm.service';
import { AnthropicLlmService } from './anthropic-llm.service';
import { EnhancedAnthropicLlmService } from '../../../chat/services/enhanced-anthropic-llm.service';

@Injectable()
export class LlmFactoryService {
  private readonly logger = new Logger(LlmFactoryService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly openaiLlmService: OpenaiLlmService,
    private readonly googleGeminiLlmService: GoogleGeminiLlmService,
    private readonly anthropicLlmService: AnthropicLlmService,
    private readonly enhancedAnthropicLlmService: EnhancedAnthropicLlmService,
  ) {}

  async getLlmService(): Promise<ILlmService> {
    try {
      // Get the current LLM provider setting from the database
      const setting = await this.prisma.appSetting.findUnique({
        where: { key: 'CURRENT_LLM_PROVIDER' },
      });

      const provider = setting?.value || 'OPENAI'; // Default to OpenAI if not set
      
      this.logger.log(`Using LLM provider: ${provider}`);

      switch (provider) {
        case 'OPENAI':
          return this.openaiLlmService;
        case 'GOOGLE_GEMINI':
          return this.googleGeminiLlmService;
        case 'ANTHROPIC':
          return this.anthropicLlmService;
        case 'ANTHROPIC_ENHANCED':
        default:
          return this.enhancedAnthropicLlmService;
      }
    } catch (error) {
      this.logger.error('Error getting LLM provider setting, defaulting to Anthropic', error.stack);
      return this.enhancedAnthropicLlmService;
    }
  }

  /**
   * Get a specific LLM service by provider name
   * Useful for testing or admin operations
   */
  getLlmServiceByProvider(provider: string): ILlmService {
    switch (provider.toUpperCase()) {
      case 'OPENAI':
        return this.openaiLlmService;
      case 'GOOGLE_GEMINI':
        return this.googleGeminiLlmService;
      case 'ANTHROPIC':
        return this.anthropicLlmService;
      case 'ANTHROPIC_ENHANCED':
      default:
        return this.enhancedAnthropicLlmService;
    }
  }

  /**
   * Get all available LLM providers
   */
  getAvailableProviders(): string[] {
    return ['OPENAI', 'GOOGLE_GEMINI', 'ANTHROPIC', 'ANTHROPIC_ENHANCED'];
  }
}
