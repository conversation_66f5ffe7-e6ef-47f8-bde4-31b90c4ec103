import { Injectable, Logger } from '@nestjs/common';
import { OpenaiService } from '../../../openai/openai.service';
import {
  ILlmService,
  LlmRecommendation,
  CandidateEntity,
  ChatResponse,
  ConversationContext,
  UserIntent,
} from '../interfaces/llm.service.interface';
import { SharedPromptBuilderService } from './shared-prompt-builder.service';

@Injectable()
export class OpenaiLlmService implements ILlmService {
  private readonly logger = new Logger(OpenaiLlmService.name);

  constructor(
    private readonly openaiService: OpenaiService,
    private readonly sharedPromptBuilder: SharedPromptBuilderService,
  ) {}

  async getRecommendation(
    problemDescription: string,
    candidateEntities: CandidateEntity[],
  ): Promise<LlmRecommendation> {
    this.logger.log(
      `Getting OpenAI recommendation for problem: "${problemDescription}" with ${candidateEntities.length} candidates`,
    );

    try {
      const prompt = this.buildRecommendationPrompt(
        problemDescription,
        candidateEntities,
      );

      // Use the existing OpenAI service to make the API call
      const response = await this.openaiService.generateCompletion(prompt);

      // Parse the response to extract entity IDs and explanation
      const recommendation = this.parseOpenAIResponse(response, candidateEntities);

      this.logger.log(
        `OpenAI recommendation generated: ${recommendation.recommendedEntityIds.length} entities recommended`,
      );

      return recommendation;
    } catch (error) {
      this.logger.error('Error generating OpenAI recommendation', error.stack);
      // Return fallback recommendation
      return this.getFallbackRecommendation(candidateEntities);
    }
  }

  private buildRecommendationPrompt(
    problemDescription: string,
    candidateEntities: CandidateEntity[],
  ): string {
    const entitiesContext = candidateEntities
      .map((entity, index) => {
        const categories = entity.categories
          .map((c) => c.category.name)
          .join(', ');
        const tags = entity.tags.map((t) => t.tag.name).join(', ');
        const features = entity.features.map((f) => f.feature.name).join(', ');

        return `${index + 1}. **${entity.name}** (ID: ${entity.id})
   - Type: ${entity.entityType.name}
   - Description: ${entity.shortDescription || entity.description || 'No description available'}
   - Categories: ${categories || 'None'}
   - Tags: ${tags || 'None'}
   - Features: ${features || 'None'}
   - Rating: ${entity.avgRating ? `${entity.avgRating}/5 (${entity.reviewCount} reviews)` : 'No ratings'}`;
      })
      .join('\n\n');

    return `You are an AI assistant helping users find the best AI tools and resources for their specific needs.

**User's Problem:**
"${problemDescription}"

**Available Options:**
${entitiesContext}

**Instructions:**
1. Analyze the user's problem and requirements
2. Recommend the TOP 3-5 most relevant options from the list above
3. Provide a clear explanation of why each recommendation fits the user's needs
4. Consider factors like: relevance to the problem, tool capabilities, user ratings, ease of use, and cost

**Response Format:**
Please respond in the following JSON format:
{
  "recommendedEntityIds": ["entity-id-1", "entity-id-2", "entity-id-3"],
  "explanation": "Based on your need for [problem summary], I recommend: 1) [Tool Name] because [specific reason]... 2) [Tool Name] because [specific reason]... 3) [Tool Name] because [specific reason]..."
}

**Important:** Only include entity IDs that exist in the provided list above. Limit to maximum 5 recommendations.`;
  }

  private parseOpenAIResponse(
    response: string,
    candidateEntities: CandidateEntity[],
  ): LlmRecommendation {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      // Validate the response structure
      if (!parsed.recommendedEntityIds || !Array.isArray(parsed.recommendedEntityIds)) {
        throw new Error('Invalid response structure');
      }

      // Filter to only include valid entity IDs
      const validEntityIds = candidateEntities.map((e) => e.id);
      const filteredIds = parsed.recommendedEntityIds.filter((id: string) =>
        validEntityIds.includes(id),
      );

      return {
        recommendedEntityIds: filteredIds.slice(0, 5), // Limit to 5
        explanation: parsed.explanation || 'AI recommendation generated successfully.',
      };
    } catch (error) {
      this.logger.warn('Failed to parse OpenAI response, using fallback', error.message);
      return this.getFallbackRecommendation(candidateEntities);
    }
  }

  private getFallbackRecommendation(candidateEntities: CandidateEntity[]): LlmRecommendation {
    // Simple fallback: return top 3 entities by rating, or first 3 if no ratings
    const sortedEntities = candidateEntities
      .sort((a, b) => (b.avgRating || 0) - (a.avgRating || 0))
      .slice(0, 3);

    return {
      recommendedEntityIds: sortedEntities.map((e) => e.id),
      explanation:
        'Based on the available options, here are the top-rated tools that might help with your needs. Please review each option to see which best fits your specific requirements.',
    };
  }

  // Chat-specific methods implementation
  async getChatResponse(
    userMessage: string,
    context: ConversationContext,
    candidateEntities?: CandidateEntity[],
  ): Promise<ChatResponse> {
    const startTime = Date.now();

    this.logger.log(
      `Getting OpenAI chat response for session: ${context.sessionId}, stage: ${context.conversationStage}`,
    );

    try {
      // First classify the intent
      const intent = await this.classifyIntent(userMessage, context);

      // Build the chat prompt based on intent and context
      const prompt = this.buildChatPrompt(userMessage, context, intent, candidateEntities);

      // 🚨 DIAGNOSTIC: Log prompt details
      this.logger.log(`📝 PROMPT ANALYSIS:`, {
        promptLength: prompt.length,
        promptSizeKB: (prompt.length / 1024).toFixed(2),
        candidateEntitiesInPrompt: candidateEntities?.length || 0,
        conversationStage: context.conversationStage,
        sessionId: context.sessionId,
        promptPreview: prompt.substring(0, 200) + '...'
      });

      if (prompt.length < 2048) {
        this.logger.warn(`🚨 PROMPT TOO SHORT: ${prompt.length} chars (expected > 2KB)`, {
          sessionId: context.sessionId,
          candidateEntitiesCount: candidateEntities?.length || 0
        });
      }

      // Get response from OpenAI
      const response = await this.openaiService.generateCompletion(prompt);

      // 🔍 DEBUG: Log the raw OpenAI response to understand parsing failures
      this.logger.log(`🔍 DEBUG RAW OPENAI RESPONSE - Session: ${context.sessionId}`);
      this.logger.log(`🔍 DEBUG RAW OPENAI RESPONSE - Length: ${response?.length || 0} characters`);
      this.logger.log(`🔍 DEBUG RAW OPENAI RESPONSE - Content:`);
      this.logger.log(`${response || 'NO RESPONSE'}`);
      this.logger.log(`🔍 DEBUG RAW OPENAI RESPONSE - End of raw response`);
      this.logger.log('='.repeat(80));

      // Parse the chat response
      const chatResponse = this.parseChatResponse(response, intent, context, candidateEntities);

      // Add metadata
      chatResponse.metadata = {
        responseTime: Date.now() - startTime,
        llmProvider: 'OPENAI',
      };

      this.logger.log(
        `OpenAI chat response generated in ${chatResponse.metadata.responseTime}ms`,
      );

      return chatResponse;
    } catch (error) {
      this.logger.error('Error generating OpenAI chat response', error.stack);
      return this.getFallbackChatResponse(userMessage, context);
    }
  }

  async classifyIntent(
    userMessage: string,
    context: ConversationContext,
  ): Promise<UserIntent> {
    this.logger.log(`Classifying intent for message: "${userMessage}"`);

    try {
      const prompt = this.buildIntentClassificationPrompt(userMessage, context);
      const response = await this.openaiService.generateCompletion(prompt);
      return this.parseIntentResponse(response);
    } catch (error) {
      this.logger.error('Error classifying intent', error.stack);
      return this.getFallbackIntent();
    }
  }

  async generateFollowUpQuestions(
    context: ConversationContext,
  ): Promise<string[]> {
    this.logger.log(`Generating follow-up questions for session: ${context.sessionId}`);

    try {
      const prompt = this.buildFollowUpPrompt(context);
      const response = await this.openaiService.generateCompletion(prompt);
      return this.parseFollowUpResponse(response);
    } catch (error) {
      this.logger.error('Error generating follow-up questions', error.stack);
      return this.getFallbackFollowUpQuestions(context);
    }
  }

  async shouldTransitionToRecommendations(
    context: ConversationContext,
  ): Promise<{ shouldTransition: boolean; reason: string }> {
    this.logger.log(`Evaluating transition for session: ${context.sessionId}`);

    try {
      const prompt = this.buildTransitionPrompt(context);
      const response = await this.openaiService.generateCompletion(prompt);
      return this.parseTransitionResponse(response);
    } catch (error) {
      this.logger.error('Error evaluating transition', error.stack);
      return { shouldTransition: false, reason: 'Error in evaluation' };
    }
  }

  // Private helper methods for chat functionality
  private buildChatPrompt(
    userMessage: string,
    context: ConversationContext,
    intent: UserIntent,
    candidateEntities?: CandidateEntity[],
  ): string {
    // Use more conversation history for better context awareness
    const conversationHistory = context.messages
      .slice(-10) // Increased from 5 to 10 messages for better context
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');

    // 🔍 DEBUG: Log what conversation history the LLM is seeing
    this.logger.log(`🔍 DEBUG LLM Context - Session: ${context.sessionId}`);
    this.logger.log(`🔍 DEBUG LLM Context - Total messages: ${context.messages?.length || 0}`);
    this.logger.log(`🔍 DEBUG LLM Context - Conversation history being sent to LLM:`);
    this.logger.log(`${conversationHistory || 'NO CONVERSATION HISTORY'}`);
    this.logger.log(`🔍 DEBUG LLM Context - Current user message: "${userMessage}"`);
    this.logger.log(`🔍 DEBUG LLM Context - End of context debug`);
    this.logger.log('='.repeat(80));

    const entitiesContext = candidateEntities
      ? this.formatEntitiesForChat(candidateEntities)
      : '';

    const userProfile = this.formatUserProfile(context);

    // Extract topics and questions already discussed
    const discussedTopics = this.extractDiscussedTopics(context);
    const previousQuestions = this.extractPreviousQuestions(context);

    // Check if user is repeating the same question
    const userMessages = context.messages?.filter(m => m.role === 'user') || [];
    const currentMessageLower = userMessage.toLowerCase();
    const isRepeatedQuestion = userMessages.some(msg =>
      msg.content.toLowerCase() === currentMessageLower &&
      msg.content !== userMessage // Not the current message
    );

    // 🚀 Use shared prompt builder for consistent rules across all providers
    const systemPrompt = this.sharedPromptBuilder.buildSystemPrompt(
      context,
      candidateEntities,
      [
        // OpenAI-specific additional rules
        'Respond in a conversational, helpful tone',
        'Be specific and actionable in your recommendations',
        'Include tool IDs in your response for validation'
      ]
    );

    const stageInstructions = this.sharedPromptBuilder.getStageInstructions(
      context.conversationStage,
      !!(candidateEntities && candidateEntities.length > 0)
    );

    // Build the complete prompt using shared prompt builder and OpenAI-specific context
    const basePrompt = systemPrompt;
    const contextualInstructions = `

**STAGE-SPECIFIC INSTRUCTIONS:**
${stageInstructions}

**CRITICAL: ANTI-REPETITION RULES**
- NEVER ask questions that have already been asked in this conversation
- NEVER repeat the same topics or suggestions you've already covered
- BUILD upon previous knowledge rather than starting over
- If the user has already provided information, acknowledge it and move forward
- Vary your language and approach even when covering similar ground
- If the user asks the same question again, acknowledge it and provide a DIFFERENT perspective or ask for clarification
- ALWAYS provide unique, varied responses even for similar questions

**Current Conversation Context:**
- Stage: ${context.conversationStage}
- Intent: ${intent.type} (confidence: ${intent.confidence})
- Session: ${context.sessionId}
- Messages in conversation: ${context.messages?.length || 0}

**User Profile:**
${userProfile}

**Full Conversation History:**
${conversationHistory}

**Topics Already Discussed:**
${discussedTopics.length > 0 ? discussedTopics.join(', ') : 'None yet'}

**Questions Already Asked:**
${previousQuestions.length > 0 ? previousQuestions.join('\n- ') : 'None yet'}

**Current User Message:**
"${userMessage}"

${entitiesContext ? `**Relevant AI Tools to Consider:**\n${entitiesContext}` : ''}

**Instructions:**
1. CAREFULLY READ the conversation history above and use it to inform your response
2. If the user has provided personal information (name, work field, etc.), acknowledge and use it
3. If the user asks about information they previously shared, answer based on the conversation history
4. Respond naturally and conversationally to the user's message
5. Based on the intent (${intent.type}), guide the conversation appropriately
6. ONLY mention AI tools that are explicitly listed in the "CRITICAL RESTRICTION" section above
7. NEVER mention tools that are not in our database (no Lumen5, Promo.com, etc.)
8. If entities are provided, mention relevant ones naturally in your response
9. ONLY ask NEW questions that haven't been covered before
10. Build upon information already gathered rather than re-asking
11. Be encouraging and helpful throughout the conversation
12. If you've already asked about their work/industry/needs, don't ask again
13. Progress the conversation forward based on what you already know
14. Provide a unique, helpful response that builds on the conversation context
15. NEVER give identical responses - always vary your language and approach
16. REMEMBER: The conversation history is your memory - use it!

**CRITICAL: You MUST respond ONLY with valid JSON. Do not include any text before or after the JSON.**

**Response Format (JSON ONLY):**
{
  "message": "Your conversational response here",
  "discoveredEntities": [{"id": "entity-id", "name": "Tool Name", "relevanceScore": 0.9, "reason": "Why it's relevant"}],
  "followUpQuestions": ["Only NEW questions that haven't been asked before"],
  "suggestedActions": [{"type": "ask_question", "label": "Tell me more about...", "data": {}}],
  "shouldTransitionToRecommendations": false,
  "conversationStage": "${context.conversationStage}"
}

**CRITICAL: discoveredEntities EXTRACTION RULES**
- If you mention ANY AI tools in your message, you MUST include them in discoveredEntities
- For EVERY tool you mention by name, add an entry to discoveredEntities with:
  * The exact ID from the "CRITICAL RESTRICTION" section above
  * The exact name as listed in the restriction section
  * A relevanceScore between 0.1-1.0 based on how well it matches their needs
  * A brief reason why it's relevant to their query
- If you mention 3 tools in your message, discoveredEntities must have 3 entries
- If you mention 0 tools in your message, discoveredEntities should be empty []
- NEVER leave discoveredEntities empty if you mention tools in your message

IMPORTANT:
- Your entire response must be valid JSON
- Do not add any explanatory text outside the JSON
- Do not use markdown formatting
- Keep your message natural, helpful, and engaging
- Focus on understanding their needs and guiding them toward the right AI tools WITHOUT repeating yourself
- Use the conversation history to provide contextual responses that build on previous exchanges`;

    // Combine all parts of the prompt
    return basePrompt + contextualInstructions;
  }

  private buildIntentClassificationPrompt(
    userMessage: string,
    context: ConversationContext,
  ): string {
    const recentMessages = context.messages
      .slice(-3)
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');

    return `Analyze the user's intent from their message and conversation context.

**Conversation Context:**
${recentMessages}

**Current User Message:**
"${userMessage}"

**Intent Types:**
- discovery: User is exploring what AI tools exist for their needs
- comparison: User wants to compare specific tools or categories
- specific_tool: User is asking about a particular tool
- general_question: User has general questions about AI tools
- refinement: User is narrowing down their requirements

**Response Format (JSON):**
{
  "type": "discovery|comparison|specific_tool|general_question|refinement",
  "confidence": 0.85,
  "entities": ["mentioned entity names"],
  "categories": ["mentioned categories"],
  "features": ["mentioned features"],
  "constraints": {
    "budget": "free|low|medium|high",
    "technical_level": "beginner|intermediate|advanced",
    "use_case": "specific use case if mentioned"
  }
}`;
  }

  private buildFollowUpPrompt(context: ConversationContext): string {
    const lastMessage = context.messages[context.messages.length - 1];

    return `Generate 2-3 intelligent follow-up questions to help the user discover the right AI tools.

**Conversation Stage:** ${context.conversationStage}
**User's Last Message:** "${lastMessage?.content || 'No previous message'}"
**Discovered Entities:** ${context.discoveredEntities.length} tools found so far

**Guidelines:**
- Ask questions that help narrow down their specific needs
- Consider their technical level and use case
- Be conversational and helpful
- Focus on practical aspects like budget, features, or use cases

**Response Format (JSON):**
{
  "questions": ["Question 1?", "Question 2?", "Question 3?"]
}`;
  }

  private buildTransitionPrompt(context: ConversationContext): string {
    return `Determine if the conversation is ready to transition to formal recommendations.

**Conversation Stage:** ${context.conversationStage}
**Messages Count:** ${context.messages.length}
**Discovered Entities:** ${context.discoveredEntities.length}
**User Preferences:** ${JSON.stringify(context.userPreferences)}

**Criteria for Transition:**
- User has clearly expressed their needs
- We have enough information about their requirements
- User seems ready for specific recommendations
- Conversation has progressed beyond initial discovery

**Response Format (JSON):**
{
  "shouldTransition": true/false,
  "reason": "Explanation of the decision"
}`;
  }

  // Parsing methods
  private parseChatResponse(
    response: string,
    intent: UserIntent,
    context: ConversationContext,
    candidateEntities?: CandidateEntity[],
  ): ChatResponse {
    try {
      // 🔍 DEBUG: Log parsing attempt details
      this.logger.log(`🔍 DEBUG PARSING - Session: ${context.sessionId}`);
      this.logger.log(`🔍 DEBUG PARSING - Response length: ${response?.length || 0}`);

      // Try multiple JSON extraction strategies
      let jsonMatch = response.match(/\{[\s\S]*\}/);

      // If no match, try to find JSON between code blocks
      if (!jsonMatch) {
        jsonMatch = response.match(/```json\s*(\{[\s\S]*?\})\s*```/);
        if (jsonMatch) {
          jsonMatch[0] = jsonMatch[1]; // Use the captured group
        }
      }

      // If still no match, try to extract just the JSON part if response starts with {
      if (!jsonMatch && response.trim().startsWith('{')) {
        jsonMatch = [response.trim()];
      }

      this.logger.log(`🔍 DEBUG PARSING - JSON match found: ${!!jsonMatch}`);

      if (!jsonMatch) {
        this.logger.log(`🔍 DEBUG PARSING - No JSON found, response was: "${response}"`);
        throw new Error('No JSON found in response');
      }

      this.logger.log(`🔍 DEBUG PARSING - Matched JSON: ${jsonMatch[0]}`);
      const parsed = JSON.parse(jsonMatch[0]);
      this.logger.log(`🔍 DEBUG PARSING - Successfully parsed JSON`);
      this.logger.log(`🔍 DEBUG PARSING - Parsed message: "${parsed.message}"`);
      this.logger.log('='.repeat(80));

      // 🎯 RE-ENABLED: Entity validation with proper CandidateEntity objects
      const validatedDiscoveredEntities = this.validateDiscoveredEntities(
        parsed.discoveredEntities || [],
        candidateEntities || []
      );

      // 🎯 ADD TRANSPARENCY: Include tool selection reasoning
      const transparencyActions = [];
      if (candidateEntities && candidateEntities.length > 0) {
        transparencyActions.push({
          type: 'show_entities',
          label: "Why these tools?",
          data: {
            explanation: candidateEntities.slice(0, 3).map(c => ({
              name: c.name,
              score: c.similarity?.toFixed(2) || '0.60',
              reason: `Similarity score: ${c.similarity?.toFixed(2) || '0.60'} - Found via ${(c.similarity || 0) > 0.7 ? 'vector search' : 'keyword search'}`
            })),
            totalCandidates: candidateEntities.length,
            searchMethod: (candidateEntities[0]?.similarity || 0) > 0.7 ? 'vector_search' : 'keyword_fallback'
          }
        });
      }

      const baseResponse = {
        message: parsed.message || 'I\'m here to help you find the perfect AI tools!',
        intent,
        discoveredEntities: validatedDiscoveredEntities,
        followUpQuestions: parsed.followUpQuestions || [],
        suggestedActions: [...(parsed.suggestedActions || []), ...transparencyActions],
        shouldTransitionToRecommendations: parsed.shouldTransitionToRecommendations || false,
        conversationStage: parsed.conversationStage || context.conversationStage,
        metadata: {
          responseTime: 0, // Will be set by caller
          llmProvider: 'OPENAI',
          candidateEntitiesProvided: candidateEntities?.length || 0,
          entitiesRecommended: validatedDiscoveredEntities.length,
          searchQuality: candidateEntities && candidateEntities.length > 0 ?
            ((candidateEntities[0]?.similarity || 0) > 0.7 ? 'high' : 'medium') : 'none'
        },
      };

      // 🎯 RE-ENABLED: Message content validation with refined approach
      return this.validateMessageContentForHallucination(baseResponse, candidateEntities || []);
    } catch (error) {
      // 🔍 DEBUG: Log parsing error details
      this.logger.warn(`🔍 DEBUG PARSING ERROR - Session: ${context.sessionId}`);
      this.logger.warn(`🔍 DEBUG PARSING ERROR - Error: ${error.message}`);
      this.logger.warn(`🔍 DEBUG PARSING ERROR - Response that failed: "${response}"`);
      this.logger.warn('='.repeat(80));

      this.logger.warn('Failed to parse chat response, using fallback', error.message);
      return this.getFallbackChatResponse('', context);
    }
  }

  /**
   * 🎯 CRITICAL: Validate discovered entities to prevent hallucination
   * Only allow entities that actually exist in our candidate entities list
   */
  private validateDiscoveredEntities(
    discoveredEntities: any[],
    candidateEntities: CandidateEntity[]
  ): Array<{
    id: string;
    name: string;
    relevanceScore: number;
    reason: string;
  }> {
    if (!discoveredEntities || !Array.isArray(discoveredEntities)) {
      return [];
    }

    const validEntityIds = new Set(candidateEntities.map(e => e.id));
    const validEntityNames = new Map(candidateEntities.map(e => [e.name.toLowerCase(), e]));

    const validatedEntities: Array<{
      id: string;
      name: string;
      relevanceScore: number;
      reason: string;
    }> = [];

    for (const entity of discoveredEntities) {
      let validEntity: CandidateEntity | null = null;

      // First, try to match by ID (most reliable)
      if (entity.id && validEntityIds.has(entity.id)) {
        validEntity = candidateEntities.find(e => e.id === entity.id) || null;
      }

      // If no ID match, try to match by name (case-insensitive)
      if (!validEntity && entity.name) {
        const nameLower = entity.name.toLowerCase();
        validEntity = validEntityNames.get(nameLower) || null;
      }

      // Only include if we found a valid match in our database
      if (validEntity) {
        validatedEntities.push({
          id: validEntity.id,
          name: validEntity.name, // Use the actual name from our database
          relevanceScore: Math.min(Math.max(entity.relevanceScore || 0.8, 0), 1), // Clamp between 0-1
          reason: entity.reason || `Relevant AI tool for your needs`
        });

        this.logger.debug(`Validated entity: ${validEntity.name} (${validEntity.id})`);
      } else {
        // Log hallucinated entities for monitoring
        this.logger.warn(`🚨 HALLUCINATED ENTITY DETECTED: ${JSON.stringify(entity)} - This entity does not exist in our database!`);
      }
    }

    this.logger.log(`Validated ${validatedEntities.length} out of ${discoveredEntities.length} discovered entities`);
    return validatedEntities;
  }

  /**
   * 🎯 CRITICAL: Validate message content for hallucinated tool mentions
   */
  private validateMessageContentForHallucination(
    response: ChatResponse,
    candidateEntities: CandidateEntity[]
  ): ChatResponse {
    if (!response.message) {
      return response;
    }

    const validToolNames = new Set(
      candidateEntities.map(entity => entity.name.toLowerCase())
    );

    // 🎯 REFINED: More targeted patterns to catch common hallucinated tools
    const suspiciousPatterns = [
      // Specific tools that are commonly hallucinated
      /\b(Lumen5|Promo\.com|Promo|Canva|Figma|Adobe Firefly|Synthesia|Runway ML|Runway|Jasper|Copy\.ai|Writesonic|Grammarly|Notion AI|Filmora|DaVinci Resolve|Luma|Pika|Descript|Replicate)\b/gi,
      // Common AI tool naming patterns that are likely hallucinated
      /\b([A-Z][a-z]{3,}(?:[A-Z][a-z]+)*)\s+(AI|Pro|Plus|Studio|Bot|Assistant|Generator|Creator|Maker|Builder)\b/g,
      // Major company AI products that might not be in our database
      /\b(Google|Microsoft|Meta|Facebook|Amazon|Apple|IBM|NVIDIA|Intel|Salesforce|Oracle)\s+(AI|Bard|Copilot|Assistant|Bot)\b/gi,
    ];

    let messageText = response.message;
    let foundHallucinations = false;

    for (const pattern of suspiciousPatterns) {
      const matches = messageText.match(pattern);
      if (matches) {
        for (const match of matches) {
          const toolName = match.toLowerCase().trim();

          const commonWords = ['ai', 'pro', 'plus', 'studio', 'the', 'and', 'or', 'for', 'with', 'to', 'in', 'on', 'at', 'by'];
          if (commonWords.includes(toolName) || toolName.length < 3) {
            continue;
          }

          if (!validToolNames.has(toolName) && !this.isGenericTerm(toolName)) {
            this.logger.warn(`🚨 HALLUCINATED TOOL IN MESSAGE: "${match}" - This tool is not in our database!`);
            foundHallucinations = true;

            const genericReplacement = this.getGenericReplacement(match);
            messageText = messageText.replace(new RegExp(match, 'gi'), genericReplacement);
          }
        }
      }
    }

    if (foundHallucinations) {
      this.logger.warn(`🎯 MESSAGE CONTENT SANITIZED: Removed hallucinated tool mentions`);

      // Only add note for significant hallucinations, not minor ones
      const significantHallucination = messageText.includes('[AI tool]') || messageText.includes('[video creation tool]');
      if (significantHallucination) {
        messageText += '\n\n*Note: I can only recommend AI tools that are verified and available in our database.*';
      }

      return {
        ...response,
        message: messageText,
      };
    }

    return response;
  }

  private isGenericTerm(term: string): boolean {
    const genericTerms = [
      'ai', 'tool', 'software', 'platform', 'service', 'app', 'application',
      'system', 'solution', 'technology', 'program', 'website', 'online',
      'digital', 'virtual', 'smart', 'intelligent', 'automated', 'machine',
      'learning', 'neural', 'network', 'algorithm', 'model', 'api'
    ];
    return genericTerms.includes(term.toLowerCase());
  }

  private getGenericReplacement(toolName: string): string {
    if (toolName.toLowerCase().includes('ai')) {
      return 'AI tools';
    }
    if (toolName.toLowerCase().includes('video') || toolName.toLowerCase().includes('edit')) {
      return 'video editing tools';
    }
    if (toolName.toLowerCase().includes('write') || toolName.toLowerCase().includes('text')) {
      return 'writing tools';
    }
    if (toolName.toLowerCase().includes('image') || toolName.toLowerCase().includes('photo')) {
      return 'image generation tools';
    }
    return 'AI tools';
  }

  private parseIntentResponse(response: string): UserIntent {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);

      return {
        type: parsed.type || 'discovery',
        confidence: parsed.confidence || 0.5,
        entities: parsed.entities || [],
        categories: parsed.categories || [],
        features: parsed.features || [],
        constraints: parsed.constraints || {},
      };
    } catch (error) {
      this.logger.warn('Failed to parse intent response, using fallback', error.message);
      return this.getFallbackIntent();
    }
  }

  private parseFollowUpResponse(response: string): string[] {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      return parsed.questions || [];
    } catch (error) {
      this.logger.warn('Failed to parse follow-up response, using fallback', error.message);
      return [];
    }
  }

  private parseTransitionResponse(response: string): { shouldTransition: boolean; reason: string } {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      return {
        shouldTransition: parsed.shouldTransition || false,
        reason: parsed.reason || 'Automatic evaluation',
      };
    } catch (error) {
      this.logger.warn('Failed to parse transition response, using fallback', error.message);
      return { shouldTransition: false, reason: 'Error in evaluation' };
    }
  }

  // Helper methods
  private formatEntitiesForChat(entities: CandidateEntity[]): string {
    return entities
      .slice(0, 5) // Limit to 5 entities for chat context
      .map((entity, index) => {
        // Handle the case where categories and features are already mapped arrays
        const categories = entity.categories?.map(c => c.category.name).join(', ') || 'General';
        const features = entity.features?.map(f => f.feature.name).join(', ') || 'Various features';

        return `${index + 1}. **${entity.name}**
   - ${entity.shortDescription || entity.description || 'AI tool'}
   - Categories: ${categories}
   - Key Features: ${features}
   - Rating: ${entity.avgRating ? `${entity.avgRating}/5` : 'Not rated'}`;
      })
      .join('\n\n');
  }

  private formatUserProfile(context: ConversationContext): string {
    const prefs = context.userPreferences;
    return `- Technical Level: ${prefs.technical_level || 'Not specified'}
- Budget Preference: ${prefs.budget || 'Not specified'}
- Preferred Categories: ${prefs.preferred_categories?.join(', ') || 'None specified'}
- Conversation Stage: ${context.conversationStage}
- Tools Discovered: ${context.discoveredEntities.length}`;
  }

  /**
   * Extract topics that have been discussed in the conversation
   */
  private extractDiscussedTopics(context: ConversationContext): string[] {
    const topics = new Set<string>();
    const messages = context.messages || [];

    // Common topic keywords to look for
    const topicKeywords = {
      'work/industry': ['work', 'job', 'industry', 'business', 'company', 'profession', 'career'],
      'technical level': ['beginner', 'intermediate', 'advanced', 'expert', 'technical', 'experience'],
      'budget': ['budget', 'cost', 'price', 'expensive', 'cheap', 'free', 'paid'],
      'programming': ['code', 'coding', 'programming', 'development', 'developer', 'software'],
      'education': ['education', 'teaching', 'learning', 'student', 'school', 'university'],
      'content creation': ['content', 'writing', 'video', 'image', 'design', 'creative'],
      'automation': ['automation', 'automate', 'workflow', 'process', 'efficiency'],
      'data analysis': ['data', 'analysis', 'analytics', 'insights', 'reporting'],
    };

    messages.forEach(msg => {
      const content = msg.content.toLowerCase();
      Object.entries(topicKeywords).forEach(([topic, keywords]) => {
        if (keywords.some(keyword => content.includes(keyword))) {
          topics.add(topic);
        }
      });
    });

    return Array.from(topics);
  }

  /**
   * Extract questions that have been asked by the assistant
   */
  private extractPreviousQuestions(context: ConversationContext): string[] {
    const questions: string[] = [];
    const messages = context.messages || [];

    messages.forEach(msg => {
      if (msg.role === 'assistant' && msg.content.includes('?')) {
        // Extract questions from assistant messages
        const questionMatches = msg.content.match(/[^.!]*\?/g);
        if (questionMatches) {
          questions.push(...questionMatches.map(q => q.trim()));
        }
      }
    });

    return questions;
  }

  // Fallback methods
  private getFallbackChatResponse(userMessage: string, context: ConversationContext): ChatResponse {
    // 🔍 DEBUG: Log fallback usage
    this.logger.log(`🔍 DEBUG FALLBACK - Session: ${context.sessionId}`);
    this.logger.log(`🔍 DEBUG FALLBACK - User message: "${userMessage}"`);
    this.logger.log(`🔍 DEBUG FALLBACK - Context messages: ${context.messages?.length || 0}`);

    // Try to create a context-aware fallback response
    let contextualMessage = this.createContextualFallbackMessage(userMessage, context);

    const fallbackMessages = {
      greeting: "Hello! I'm here to help you discover the perfect AI tools for your needs. What kind of tasks are you looking to accomplish?",
      discovery: "I'd love to help you find the right AI tools! Could you tell me more about what you're trying to achieve?",
      refinement: "Let me help you narrow down the options. What specific features or capabilities are most important to you?",
      recommendation: "Based on our conversation, I can help you find some great options. Would you like me to show you some specific recommendations?",
      comparison: "I can help you compare different AI tools. What specific aspects would you like me to focus on?"
    };

    // Use contextual message if available, otherwise use stage-based fallback
    const finalMessage = contextualMessage || fallbackMessages[context.conversationStage] || fallbackMessages.discovery;

    this.logger.log(`🔍 DEBUG FALLBACK - Final message: "${finalMessage}"`);
    this.logger.log('='.repeat(80));

    return {
      message: finalMessage,
      intent: this.getFallbackIntent(),
      followUpQuestions: this.getFallbackFollowUpQuestions(context),
      shouldTransitionToRecommendations: false,
      conversationStage: context.conversationStage,
      metadata: {
        responseTime: 0,
        llmProvider: 'OPENAI_FALLBACK',
      },
    };
  }

  /**
   * Create a contextual fallback message based on conversation history
   */
  private createContextualFallbackMessage(userMessage: string, context: ConversationContext): string | null {
    const messages = context.messages || [];

    // If there are previous messages, try to acknowledge them
    if (messages.length > 0) {
      const userMessages = messages.filter(m => m.role === 'user');
      const lastUserMessage = userMessages[userMessages.length - 1];

      // Look for user information in previous messages
      const userInfo = this.extractUserInfoFromMessages(messages);

      if (userInfo.name || userInfo.field || userInfo.interests.length > 0) {
        let response = "I understand you're looking for AI tools";

        if (userInfo.name) {
          response = `Hi ${userInfo.name}! I understand you're looking for AI tools`;
        }

        if (userInfo.field) {
          response += ` for your work in ${userInfo.field}`;
        }

        if (userInfo.interests.length > 0) {
          response += ` related to ${userInfo.interests.join(', ')}`;
        }

        response += ". I'm here to help you find the perfect solution. What specific features or capabilities are most important to you?";

        return response;
      }

      // If user is asking about previous information, acknowledge it
      if (userMessage.toLowerCase().includes('name') || userMessage.toLowerCase().includes('work') || userMessage.toLowerCase().includes('field')) {
        return "I can see you're asking about information from our conversation. I'm here to help you find the right AI tools based on your needs. Could you tell me more about what you're looking for?";
      }
    }

    return null;
  }

  /**
   * Extract user information from conversation messages
   */
  private extractUserInfoFromMessages(messages: any[]): { name: string | null, field: string | null, interests: string[] } {
    let name: string | null = null;
    let field: string | null = null;
    const interests: string[] = [];

    messages.forEach(msg => {
      if (msg.role === 'user') {
        const content = msg.content.toLowerCase();

        // Extract name
        const nameMatch = content.match(/my name is (\w+)/i) || content.match(/i'm (\w+)/i) || content.match(/i am (\w+)/i);
        if (nameMatch) {
          name = nameMatch[1];
        }

        // Extract field/work
        const fieldMatch = content.match(/i work in (\w+)/i) || content.match(/work in (\w+)/i) || content.match(/field of (\w+)/i);
        if (fieldMatch) {
          field = fieldMatch[1];
        }

        // Extract interests
        if (content.includes('education')) interests.push('education');
        if (content.includes('development') || content.includes('coding')) interests.push('development');
        if (content.includes('design')) interests.push('design');
        if (content.includes('marketing')) interests.push('marketing');
        if (content.includes('content')) interests.push('content creation');
      }
    });

    return { name, field, interests: [...new Set(interests)] };
  }

  private getFallbackIntent(): UserIntent {
    return {
      type: 'discovery',
      confidence: 0.5,
      entities: [],
      categories: [],
      features: [],
      constraints: {},
    };
  }

  private getFallbackFollowUpQuestions(context: ConversationContext): string[] {
    const questionsByStage = {
      greeting: [
        "What type of work or projects are you working on?",
        "Are you looking for tools for business or personal use?"
      ],
      discovery: [
        "What's your experience level with AI tools?",
        "Do you have a budget in mind for AI tools?",
        "What's the main challenge you're trying to solve?"
      ],
      refinement: [
        "Would you prefer free tools or are you open to paid options?",
        "How technical do you want the tool to be?",
        "Do you need integration with other software?"
      ],
      recommendation: [
        "Would you like to see some specific recommendations?",
        "Should I focus on the most popular options or newer tools?"
      ],
      comparison: [
        "What criteria are most important for your decision?",
        "Would you like me to compare pricing or features?"
      ]
    };

    return questionsByStage[context.conversationStage] || questionsByStage.discovery;
  }
}
