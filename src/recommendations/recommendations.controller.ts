import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';
import { SupabaseAuthGuard } from '../auth/guards/supabase-auth.guard';
import { RecommendationsService } from './recommendations.service';
import { CreateRecommendationDto } from './dto/create-recommendation.dto';
import { RecommendationResponseDto } from './dto/recommendation-response.dto';

@ApiTags('Recommendations')
@Controller('recommendations')
// @UseGuards(SupabaseAuthGuard) // Temporarily disabled for testing
@ApiBearerAuth()
export class RecommendationsController {
  constructor(private readonly recommendationsService: RecommendationsService) {}

  @Post()
  @Throttle({ default: { limit: 10, ttl: 60000 } }) // 10 requests per minute
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get AI-powered recommendations with comprehensive filtering',
    description: `
    Get personalized AI recommendations based on a problem description with access to 80+ filter parameters.

    This enhanced endpoint:
    1. Uses vector search to find semantically relevant entities
    2. Applies comprehensive filters across all entity types (Tools, Courses, Jobs, Events, Hardware, etc.)
    3. Uses the configured LLM provider to analyze and recommend the best options
    4. Returns detailed recommendations with explanations

    **NEW: Enhanced Filtering Capabilities**
    - Tool filters: technical_levels, learning_curves, has_api, frameworks, platforms, etc.
    - Course filters: skill_levels, certificate_available, instructor_name, duration, etc.
    - Job filters: employment_types, experience_levels, salary ranges, location_types, etc.
    - Event filters: event_types, is_online, location, date ranges, registration_required, etc.
    - Hardware filters: hardware_types, manufacturers, price ranges, memory/processor specs, etc.
    - And 60+ more entity-specific filters for comprehensive discovery

    The LLM provider can be configured by admins via the admin settings API.
    `,
  })
  @ApiBody({
    type: CreateRecommendationDto,
    description: 'Problem description and comprehensive filters for precise recommendations',
    examples: {
      'Beginner AI Tool with API': {
        summary: 'AI tool for beginners with API access',
        value: {
          problem_description: 'I need an AI tool to help me generate code documentation automatically for my Python projects',
          filters: {
            entityTypeIds: ['ai-tool'],
            technical_levels: ['BEGINNER', 'INTERMEDIATE'],
            has_api: true,
            has_free_tier: true,
            frameworks: ['Python'],
            platforms: ['Web', 'Linux'],
            use_cases_search: 'documentation',
            max_candidates: 15,
          },
        },
      },
      'Senior ML Job Remote': {
        summary: 'Senior ML engineering job, remote, high salary',
        value: {
          problem_description: 'I want to find a senior machine learning engineering position that pays well and allows remote work',
          filters: {
            entityTypeIds: ['job'],
            experience_levels: ['SENIOR', 'LEAD'],
            employment_types: ['FULL_TIME'],
            location_types: ['Remote'],
            salary_min: 120,
            job_description: 'machine learning',
            max_candidates: 20,
          },
        },
      },
      'AI Course with Certificate': {
        summary: 'AI course for beginners with certificate',
        value: {
          problem_description: 'I am a beginner and want to learn about artificial intelligence with a certificate',
          filters: {
            entityTypeIds: ['course'],
            skill_levels: ['BEGINNER'],
            certificate_available: true,
            searchTerm: 'artificial intelligence',
            duration_text: 'weeks',
            max_candidates: 10,
          },
        },
      },
      'AI Conference Online 2024': {
        summary: 'Online AI conferences in 2024',
        value: {
          problem_description: 'I want to attend AI conferences to learn about the latest developments',
          filters: {
            entityTypeIds: ['event'],
            event_types: ['Conference'],
            is_online: true,
            start_date_from: '2024-01-01',
            start_date_to: '2024-12-31',
            searchTerm: 'artificial intelligence',
            max_candidates: 15,
          },
        },
      },
      'GPU Under $2000': {
        summary: 'GPU for AI development under $2000',
        value: {
          problem_description: 'I need a powerful GPU for training machine learning models on a budget',
          filters: {
            entityTypeIds: ['hardware'],
            hardware_types: ['GPU'],
            price_max: 2000,
            memory_search: '16GB',
            manufacturers: ['NVIDIA'],
            max_candidates: 10,
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'AI recommendations generated successfully',
    type: RecommendationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request data',
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
  })
  @ApiResponse({
    status: 429,
    description: 'Rate limit exceeded',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error during recommendation generation',
  })
  async getRecommendations(
    @Body() createRecommendationDto: CreateRecommendationDto,
  ): Promise<RecommendationResponseDto> {
    return this.recommendationsService.getRecommendations(createRecommendationDto);
  }
}
