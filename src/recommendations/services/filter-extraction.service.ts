import { Injectable, Logger } from '@nestjs/common';
import { RecommendationFiltersDto } from '../dto/recommendation-filters.dto';
import { EntitiesService } from '../../entities/entities.service';

/**
 * Service for extracting filter criteria from natural language descriptions
 * This enables the recommendation system to automatically apply relevant filters
 * based on user intent expressed in natural language.
 */
@Injectable()
export class FilterExtractionService {
  private readonly logger = new Logger(FilterExtractionService.name);

  /**
   * Extract filter criteria from a problem description
   */
  async extractFiltersFromDescription(description: string): Promise<Partial<RecommendationFiltersDto>> {
    const normalizedDescription = description.toLowerCase();
    const extractedFilters: Partial<RecommendationFiltersDto> = {};

    try {
      // Extract entity types
      extractedFilters.entityTypeIds = this.extractEntityTypes(normalizedDescription);

      // Extract technical level indicators
      const technicalLevels = this.extractTechnicalLevels(normalizedDescription);
      if (technicalLevels.length > 0) {
        extractedFilters.technical_levels = technicalLevels as any;
      }

      // Extract budget/pricing indicators
      this.extractBudgetFilters(normalizedDescription, extractedFilters);

      // Extract platform/framework preferences
      extractedFilters.platforms = this.extractPlatforms(normalizedDescription);
      extractedFilters.frameworks = this.extractFrameworks(normalizedDescription);

      // Extract use case and feature requirements
      extractedFilters.use_cases_search = this.extractUseCases(normalizedDescription);
      extractedFilters.key_features_search = this.extractKeyFeatures(normalizedDescription);

      // Extract job-specific criteria
      this.extractJobFilters(normalizedDescription, extractedFilters);

      // Extract course-specific criteria
      this.extractCourseFilters(normalizedDescription, extractedFilters);

      // Extract event-specific criteria
      this.extractEventFilters(normalizedDescription, extractedFilters);

      // Extract hardware-specific criteria
      this.extractHardwareFilters(normalizedDescription, extractedFilters);

      this.logger.debug('Extracted filters from description:', {
        description: description.substring(0, 100),
        extractedFilters: Object.keys(extractedFilters),
      });

      return extractedFilters;
    } catch (error) {
      this.logger.error('Error extracting filters from description', error.stack);
      return {};
    }
  }

  private extractEntityTypes(description: string): string[] {
    const entityTypes: string[] = [];

    // Tool indicators
    if (this.matchesAny(description, [
      'tool', 'software', 'platform', 'api', 'service', 'application', 'app'
    ])) {
      entityTypes.push('ai-tool');
    }

    // Course indicators
    if (this.matchesAny(description, [
      'learn', 'course', 'tutorial', 'education', 'training', 'class', 'lesson'
    ])) {
      entityTypes.push('course');
    }

    // Job indicators
    if (this.matchesAny(description, [
      'job', 'position', 'career', 'work', 'employment', 'hire', 'role'
    ])) {
      entityTypes.push('job');
    }

    // Event indicators
    if (this.matchesAny(description, [
      'conference', 'event', 'meetup', 'workshop', 'webinar', 'summit'
    ])) {
      entityTypes.push('event');
    }

    // Hardware indicators
    if (this.matchesAny(description, [
      'gpu', 'hardware', 'computer', 'server', 'chip', 'processor'
    ])) {
      entityTypes.push('hardware');
    }

    return entityTypes;
  }

  private extractTechnicalLevels(description: string): string[] {
    const levels: string[] = [];

    if (this.matchesAny(description, ['beginner', 'new', 'start', 'basic', 'simple'])) {
      levels.push('BEGINNER');
    }
    if (this.matchesAny(description, ['intermediate', 'some experience', 'moderate'])) {
      levels.push('INTERMEDIATE');
    }
    if (this.matchesAny(description, ['advanced', 'expert', 'professional', 'complex'])) {
      levels.push('ADVANCED');
    }
    if (this.matchesAny(description, ['expert', 'master', 'senior', 'lead'])) {
      levels.push('EXPERT');
    }

    return levels;
  }

  private extractBudgetFilters(description: string, filters: Partial<RecommendationFiltersDto>): void {
    if (this.matchesAny(description, ['free', 'no cost', 'zero cost', 'gratis'])) {
      filters.has_free_tier = true;
      filters.price_range = 'FREE';
    }

    if (this.matchesAny(description, ['budget', 'cheap', 'affordable', 'low cost'])) {
      filters.price_range = 'LOW';
    }

    // Extract specific price mentions
    const priceMatch = description.match(/\$(\d+)/);
    if (priceMatch) {
      const price = parseInt(priceMatch[1]);
      if (price <= 50) {
        filters.price_range = 'LOW';
      } else if (price <= 200) {
        filters.price_range = 'MEDIUM';
      } else {
        filters.price_range = 'HIGH';
      }
    }
  }

  private extractPlatforms(description: string): string[] {
    const platforms: string[] = [];

    if (this.matchesAny(description, ['windows', 'pc'])) platforms.push('Windows');
    if (this.matchesAny(description, ['mac', 'macos', 'apple'])) platforms.push('macOS');
    if (this.matchesAny(description, ['linux', 'ubuntu'])) platforms.push('Linux');
    if (this.matchesAny(description, ['web', 'browser', 'online'])) platforms.push('Web');
    if (this.matchesAny(description, ['mobile', 'phone', 'ios', 'android'])) platforms.push('Mobile');

    return platforms;
  }

  private extractFrameworks(description: string): string[] {
    const frameworks: string[] = [];

    if (this.matchesAny(description, ['tensorflow', 'tf'])) frameworks.push('TensorFlow');
    if (this.matchesAny(description, ['pytorch', 'torch'])) frameworks.push('PyTorch');
    if (this.matchesAny(description, ['scikit', 'sklearn'])) frameworks.push('Scikit-learn');
    if (this.matchesAny(description, ['keras'])) frameworks.push('Keras');
    if (this.matchesAny(description, ['opencv'])) frameworks.push('OpenCV');

    return frameworks;
  }

  private extractUseCases(description: string): string {
    const useCases = [
      'content creation', 'data analysis', 'machine learning', 'natural language processing',
      'computer vision', 'automation', 'chatbot', 'recommendation', 'prediction',
      'classification', 'generation', 'translation', 'summarization'
    ];

    for (const useCase of useCases) {
      if (description.includes(useCase)) {
        return useCase;
      }
    }

    return '';
  }

  private extractKeyFeatures(description: string): string {
    const features = [
      'api access', 'real-time', 'batch processing', 'cloud', 'on-premise',
      'scalable', 'secure', 'fast', 'accurate', 'customizable'
    ];

    for (const feature of features) {
      if (description.includes(feature)) {
        return feature;
      }
    }

    return '';
  }

  private extractJobFilters(description: string, filters: Partial<RecommendationFiltersDto>): void {
    // Employment types
    if (this.matchesAny(description, ['full time', 'full-time'])) {
      filters.employment_types = ['FULL_TIME'];
    }
    if (this.matchesAny(description, ['part time', 'part-time'])) {
      filters.employment_types = ['PART_TIME'];
    }
    if (this.matchesAny(description, ['contract', 'contractor'])) {
      filters.employment_types = ['CONTRACT'];
    }
    if (this.matchesAny(description, ['remote', 'work from home'])) {
      filters.location_types = ['Remote'];
    }

    // Experience levels
    if (this.matchesAny(description, ['entry level', 'junior'])) {
      filters.experience_levels = ['ENTRY', 'JUNIOR'];
    }
    if (this.matchesAny(description, ['senior', 'lead'])) {
      filters.experience_levels = ['SENIOR', 'LEAD'];
    }

    // Salary extraction
    const salaryMatch = description.match(/\$(\d+)k?/);
    if (salaryMatch) {
      const salary = parseInt(salaryMatch[1]);
      if (salary > 1000) {
        filters.salary_min = Math.floor(salary / 1000);
      } else {
        filters.salary_min = salary;
      }
    }
  }

  private extractCourseFilters(description: string, filters: Partial<RecommendationFiltersDto>): void {
    if (this.matchesAny(description, ['certificate', 'certification', 'certified'])) {
      filters.certificate_available = true;
    }

    // Duration extraction
    const durationMatch = description.match(/(\d+)\s*(week|month|hour)/);
    if (durationMatch) {
      filters.duration_text = `${durationMatch[1]} ${durationMatch[2]}`;
    }
  }

  private extractEventFilters(description: string, filters: Partial<RecommendationFiltersDto>): void {
    if (this.matchesAny(description, ['online', 'virtual', 'webinar'])) {
      filters.is_online = true;
    }

    if (this.matchesAny(description, ['conference'])) {
      filters.event_types = ['Conference'];
    }
    if (this.matchesAny(description, ['workshop'])) {
      filters.event_types = ['Workshop'];
    }
    if (this.matchesAny(description, ['meetup'])) {
      filters.event_types = ['Meetup'];
    }
  }

  private extractHardwareFilters(description: string, filters: Partial<RecommendationFiltersDto>): void {
    if (this.matchesAny(description, ['gpu', 'graphics card'])) {
      filters.hardware_types = ['GPU'];
    }
    if (this.matchesAny(description, ['cpu', 'processor'])) {
      filters.hardware_types = ['CPU'];
    }

    if (this.matchesAny(description, ['nvidia'])) {
      filters.manufacturers = ['NVIDIA'];
    }
    if (this.matchesAny(description, ['amd'])) {
      filters.manufacturers = ['AMD'];
    }
    if (this.matchesAny(description, ['intel'])) {
      filters.manufacturers = ['Intel'];
    }

    // Memory extraction
    const memoryMatch = description.match(/(\d+)gb/i);
    if (memoryMatch) {
      filters.memory_search = `${memoryMatch[1]}GB`;
    }
  }

  private matchesAny(text: string, patterns: string[]): boolean {
    return patterns.some(pattern => text.includes(pattern));
  }
}
