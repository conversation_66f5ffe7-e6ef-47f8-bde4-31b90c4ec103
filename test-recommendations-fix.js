#!/usr/bin/env node

/**
 * Test script to verify the recommendations endpoint is working after fixes
 */

const https = require('https');
const http = require('http');

const BASE_URL = 'http://localhost:3001';

// Test payload for recommendations
const testPayload = {
  problem_description: "I need an AI tool for testing and development",
  filters: {
    max_candidates: 5,
    entityTypeIds: ['fd181400-c9e6-431c-a8bd-c068d0491aba'], // AI Tool UUID
    technical_levels: ['BEGINNER', 'INTERMEDIATE'],
    has_api: true
  }
};

function makeRequest(url, method = 'GET', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token',
        ...headers
      }
    };

    if (data) {
      const jsonData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(jsonData);
    }

    const client = urlObj.protocol === 'https:' ? https : http;
    const req = client.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testRecommendationsEndpoint() {
  console.log('🧪 Testing Recommendations Endpoint Fix');
  console.log('=====================================');
  
  try {
    // Test 1: Health check
    console.log('\n1. Testing server health...');
    try {
      const healthResponse = await makeRequest(`${BASE_URL}/`);
      console.log(`✅ Server is running (Status: ${healthResponse.statusCode})`);
    } catch (error) {
      console.log(`❌ Server is not running: ${error.message}`);
      return;
    }

    // Test 2: Recommendations endpoint
    console.log('\n2. Testing recommendations endpoint...');
    const response = await makeRequest(
      `${BASE_URL}/recommendations`,
      'POST',
      testPayload
    );

    console.log(`Status Code: ${response.statusCode}`);
    
    if (response.statusCode === 200) {
      console.log('✅ Recommendations endpoint is working!');
      console.log('\nResponse structure:');
      console.log(`- recommended_entities: ${response.data.recommended_entities?.length || 0} items`);
      console.log(`- explanation: ${response.data.explanation ? 'Present' : 'Missing'}`);
      console.log(`- candidates_analyzed: ${response.data.candidates_analyzed || 0}`);
      console.log(`- llm_provider: ${response.data.llm_provider || 'Unknown'}`);
      
      if (response.data.recommended_entities?.length > 0) {
        console.log('\nFirst recommended entity:');
        const firstEntity = response.data.recommended_entities[0];
        console.log(`- ID: ${firstEntity.id}`);
        console.log(`- Name: ${firstEntity.name}`);
        console.log(`- Type: ${firstEntity.entityType?.name || 'Unknown'}`);
      }
    } else if (response.statusCode === 401) {
      console.log('❌ Authentication error - check auth configuration');
    } else if (response.statusCode === 500) {
      console.log('❌ Internal server error - check logs for details');
      console.log('Response:', JSON.stringify(response.data, null, 2));
    } else {
      console.log(`❌ Unexpected status code: ${response.statusCode}`);
      console.log('Response:', JSON.stringify(response.data, null, 2));
    }

    // Test 3: Test with minimal payload
    console.log('\n3. Testing with minimal payload...');
    const minimalPayload = {
      problem_description: "I need help finding AI tools",
      filters: {
        max_candidates: 5
      }
    };
    
    const minimalResponse = await makeRequest(
      `${BASE_URL}/recommendations`,
      'POST',
      minimalPayload
    );
    
    console.log(`Minimal test status: ${minimalResponse.statusCode}`);
    if (minimalResponse.statusCode === 200) {
      console.log('✅ Minimal payload test passed');
    } else {
      console.log('❌ Minimal payload test failed');
    }

  } catch (error) {
    console.log(`❌ Test failed with error: ${error.message}`);
    console.log('Stack trace:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testRecommendationsEndpoint()
    .then(() => {
      console.log('\n🏁 Test completed');
    })
    .catch((error) => {
      console.error('Test runner error:', error);
      process.exit(1);
    });
}

module.exports = { testRecommendationsEndpoint };
